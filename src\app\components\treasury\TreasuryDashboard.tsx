'use client';

import React, { useState } from 'react';
import Overview from './Overview';
import LiveReserve from './LiveReserve';
import DailyTransactions from './DailyTransactions';
import RealTimeStaking from './RealTimeStaking';
import StartupFunding from './StartupFunding';
import ROIDistribution from './ROIDistribution';
import VisualAnalytics from './VisualAnalytics';
import TreasurySideNav from './TreasurySideNav';

type TreasuryTab = 'overview' | 'live-reserve' | 'daily-transactions' | 'real-time-staking' | 'startup-funding' | 'roi-distribution' | 'visual-analytics';

const TreasuryDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TreasuryTab>('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', component: Overview },
    { id: 'live-reserve', label: 'Live Reserve', component: LiveReserve },
    { id: 'daily-transactions', label: 'Daily Transactions', component: DailyTransactions },
    { id: 'real-time-staking', label: 'Real Time Staking', component: RealTimeStaking },
    { id: 'startup-funding', label: 'Startup Funding', component: StartupFunding },
    { id: 'roi-distribution', label: 'ROI Distribution', component: ROIDistribution },
    { id: 'visual-analytics', label: 'Visual Analytics', component: VisualAnalytics },
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || Overview;

  return (
    <div className="min-h-screen bg-[#171717] relative">
      {/* Background overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/20 pointer-events-none"></div>

      <div className="relative z-10">
        {/* Header */}
        <div className="text-center pt-16 pb-8 px-10 max-w-4xl mx-auto">
          <h1 className="text-white text-6xl font-semibold mb-6 flex items-center justify-center gap-5 font-montserrat">
            <div className="flex flex-col items-center gap-1">
              Treasury
              <div className="w-72 h-1 bg-gradient-to-r from-[#D19049] to-[#BF4129] rounded-full"></div>
            </div>
            Dashboard
          </h1>
          <p className="text-neutral-300 text-lg font-montserrat leading-7">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        {/* Dashboard Layout */}
        <div className="px-5 lg:px-10 max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-10">
            {/* Sidebar */}
            <TreasurySideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Main Content */}
            <div className="flex-1 max-w-4xl">
              <ActiveComponent />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TreasuryDashboard;
