'use client';

import React, { useState } from 'react';
import Overview from './Overview';
import LiveReserve from './LiveReserve';
import DailyTransactions from './DailyTransactions';
import RealTimeStaking from './RealTimeStaking';
import StartupFunding from './StartupFunding';
import ROIDistribution from './ROIDistribution';
import VisualAnalytics from './VisualAnalytics';
import TreasurySideNav from './TreasurySideNav';
import './Overview.css';

type TreasuryTab = 'overview' | 'live-reserve' | 'daily-transactions' | 'real-time-staking' | 'startup-funding' | 'roi-distribution' | 'visual-analytics';

const TreasuryDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TreasuryTab>('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', component: Overview },
    { id: 'live-reserve', label: 'Live Reserve', component: LiveReserve },
    { id: 'daily-transactions', label: 'Daily Transactions', component: DailyTransactions },
    { id: 'real-time-staking', label: 'Real Time Staking', component: RealTimeStaking },
    { id: 'startup-funding', label: 'Startup Funding', component: StartupFunding },
    { id: 'roi-distribution', label: 'ROI Distribution', component: ROIDistribution },
    { id: 'visual-analytics', label: 'Visual Analytics', component: VisualAnalytics },
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || Overview;

  return (
    <div className="overview-container min-h-screen bg-[#171717] relative">
      {/* Background overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/20 pointer-events-none"></div>

      <div className="overview-content relative z-10">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <div className="title-span w-72 h-1 bg-gradient-to-r from-[#D19049] to-[#BF4129] rounded-full"></div>
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Dashboard Content Wrapper */}
          <div className="dashboard-content-wrapper">
            {/* Sidebar */}
            <TreasurySideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Main Content */}
            <ActiveComponent />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TreasuryDashboard;
