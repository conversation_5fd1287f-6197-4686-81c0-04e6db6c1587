'use client';

import React, { useState } from 'react';
import Overview from './Overview';
import LiveReserve from './LiveReserve';
import DailyTransactions from './DailyTransactions';
import RealTimeStaking from './RealTimeStaking';
import StartupFunding from './StartupFunding';
import ROIDistribution from './ROIDistribution';
import VisualAnalytics from './VisualAnalytics';

type TreasuryTab = 'overview' | 'live-reserve' | 'daily-transactions' | 'real-time-staking' | 'startup-funding' | 'roi-distribution' | 'visual-analytics';

const TreasuryDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TreasuryTab>('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', component: Overview },
    { id: 'live-reserve', label: 'Live Reserve', component: LiveReserve },
    { id: 'daily-transactions', label: 'Daily Transactions', component: DailyTransactions },
    { id: 'real-time-staking', label: 'Real Time Staking', component: RealTimeStaking },
    { id: 'startup-funding', label: 'Startup Funding', component: StartupFunding },
    { id: 'roi-distribution', label: 'ROI Distribution', component: ROIDistribution },
    { id: 'visual-analytics', label: 'Visual Analytics', component: VisualAnalytics },
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || Overview;

  return (
    <div className="w-full py-20">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-white text-5xl font-bold font-montserrat mb-4 flex items-center justify-center gap-4">
            Treasury
            <div className="w-24 h-1 bg-gradient-to-r from-[#D19049] to-[#BF4129] rounded-full"></div>
            Dashboard
          </h1>
          <p className="text-neutral-300 text-lg font-montserrat max-w-3xl mx-auto leading-relaxed">
            Real-time transparency into ESVC Capital's treasury operations, staking rewards, and funding distributions.
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-[#262626] rounded-2xl p-2 mb-8">
          <div className="flex flex-wrap gap-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TreasuryTab)}
                className={`px-4 py-3 rounded-xl font-montserrat text-sm font-medium transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-[#BF4129] text-white'
                    : 'text-neutral-300 hover:text-white hover:bg-neutral-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Active Tab Content */}
        <div className="min-h-[600px]">
          <ActiveComponent />
        </div>
      </div>
    </div>
  );
};

export default TreasuryDashboard;
