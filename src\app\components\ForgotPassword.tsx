'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface ForgotPasswordProps {
  onBack: () => void;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onBack }) => {
  const router = useRouter();
  const [step, setStep] = useState<'forgot' | 'verify' | 'reset'>('forgot');
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isCodeInvalid, setIsCodeInvalid] = useState(false);
  const [resendTimer, setResendTimer] = useState(119); // 1:59 in seconds

  // Timer countdown effect
  useEffect(() => {
    if (step === 'verify' && resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [step, resendTimer]);

  const handleSendCode = () => {
    // Simulate sending verification code
    setStep('verify');
    setResendTimer(119); // Reset timer
  };

  const handleChangeEmailAddress = () => {
    setStep('forgot');
    setVerificationCode(['', '', '', '', '', '']);
    setIsCodeInvalid(false);
  };

  const handleResendCode = () => {
    // Simulate resending code
    setVerificationCode(['', '', '', '', '', '']);
    setIsCodeInvalid(false);
    setResendTimer(119);
  };

  const handleCodeChange = (index: number, value: string) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);
      setIsCodeInvalid(false);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleVerifyCode = () => {
    const code = verificationCode.join('');
    if (code.length === 6) {
      // Simulate code verification
      if (code === '123456') { // Mock valid code
        setStep('reset');
      } else {
        setIsCodeInvalid(true);
      }
    }
  };

  const handleResetPassword = () => {
    // Simulate password reset
    console.log('Password reset successfully');
    onBack(); // Return to login
  };

  const isPasswordValid = (password: string) => {
    const checks = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    return checks;
  };

  const passwordChecks = isPasswordValid(newPassword);
  const allPasswordChecksValid = Object.values(passwordChecks).every(Boolean);
  const passwordsMatch = newPassword === confirmPassword && confirmPassword.length > 0;

  if (step === 'forgot') {
    return (
      <div className="min-h-screen relative overflow-hidden font-montserrat bg-neutral-900">
        <div className="relative z-10 min-h-screen px-6 py-10 flex justify-center items-start">
          <button 
            className="absolute top-8 left-6 bg-transparent border-none text-[#D19049] font-montserrat text-base font-medium cursor-pointer flex items-center gap-2 transition-colors duration-300 hover:text-[#BF4129]" 
            onClick={onBack}
          >
            <span className="text-lg">←</span> Go Back
          </button>
          
          <div className="flex flex-col items-center p-6 gap-10 absolute w-[560px] bg-[#262626] rounded-3xl text-center top-20 max-w-[calc(100vw-48px)]">
            <div>
              <h1 className="text-white text-4xl font-bold m-0 leading-tight">Forgot Your Password?</h1>
              <p className="text-[#CCCCCC] text-lg font-medium m-0 mt-4 leading-relaxed">We'll send you a verification code to reset your password</p>
            </div>

            <div className="w-full text-left">
              <label htmlFor="email" className="block text-white text-sm font-medium mb-2">Email Address</label>
              <div className="relative flex items-center">
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full h-14 bg-[#404040] border border-[#525252] rounded-xl px-5 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:bg-[#4A4A4A] placeholder-[#999999]"
                />
              </div>
            </div>

            <button
              className="w-full h-14 bg-[#BF4129] border-none rounded-xl text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 hover:bg-[#D19049] hover:-translate-y-0.5 disabled:bg-[#666666] disabled:cursor-not-allowed disabled:transform-none"
              onClick={handleSendCode}
              disabled={!email}
            >
              Send Verification Code
            </button>

            <button 
              className="bg-transparent border-none text-[#CCCCCC] font-montserrat text-sm font-medium cursor-pointer flex items-center justify-center gap-2 w-full mt-6 transition-colors duration-300 hover:text-white" 
              onClick={() => router.push('/login')}
            >
              <span>←</span> Back to Login
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (step === 'verify') {
    return (
      <div className="min-h-screen relative overflow-hidden font-montserrat bg-neutral-900">
        <div className="relative z-10 min-h-screen px-6 py-10 flex justify-center items-start">
          <button 
            className="absolute top-8 left-6 bg-transparent border-none text-[#D19049] font-montserrat text-base font-medium cursor-pointer flex items-center gap-2 transition-colors duration-300 hover:text-[#BF4129]" 
            onClick={onBack}
          >
            <span className="text-lg">←</span> Go Back
          </button>
          
          <div className="flex flex-col items-center p-6 gap-10 absolute w-[560px] min-h-[528px] bg-[#262626] rounded-3xl text-center top-20 max-w-[calc(100vw-48px)]">
            <div>
              <h1 className="text-white text-4xl font-bold m-0 leading-tight">Verify Your Email</h1>
              <p className="text-[#CCCCCC] text-lg font-medium m-0 mt-4 leading-relaxed">Enter the 6-digit code sent to your email <strong>{email}</strong> to continue</p>
            </div>

            <div className="flex gap-3 justify-center w-full">
              {verificationCode.map((digit, index) => (
                <input
                  key={index}
                  id={`code-${index}`}
                  type="text"
                  value={digit}
                  onChange={(e) => handleCodeChange(index, e.target.value)}
                  className={`w-14 h-14 bg-[#404040] border rounded-xl text-center text-white font-montserrat text-2xl font-semibold transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:bg-[#4A4A4A] ${isCodeInvalid ? 'border-[#FF4444] bg-[rgba(255,68,68,0.1)]' : 'border-[#525252]'}`}
                  maxLength={1}
                />
              ))}
            </div>

            {isCodeInvalid && (
              <p className="text-[#FF4444] text-sm m-0 text-center">Invalid or expired code. Please check and try again.</p>
            )}

            <button
              className="w-full h-14 bg-[#BF4129] border-none rounded-xl text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 hover:bg-[#D19049] hover:-translate-y-0.5 disabled:bg-[#666666] disabled:cursor-not-allowed disabled:transform-none"
              onClick={handleVerifyCode}
              disabled={verificationCode.join('').length !== 6}
            >
              Verify Code
            </button>

            <div className="text-center w-full">
              <p className="text-[#CCCCCC] text-sm m-0 mb-2">Check your spam folder if you don't see it.</p>
              <button
                className="bg-transparent border-none text-[#BF4129] font-montserrat text-sm font-medium cursor-pointer underline disabled:text-[#666666] disabled:cursor-not-allowed disabled:no-underline hover:text-[#D19049] disabled:hover:text-[#666666]"
                onClick={handleResendCode}
                disabled={resendTimer > 0}
              >
                {resendTimer > 0
                  ? `Resend Code in ${Math.floor(resendTimer / 60)}:${(resendTimer % 60).toString().padStart(2, '0')}`
                  : 'Resend Code'
                }
              </button>
            </div>

            <button 
              className="bg-transparent border-none text-[#CCCCCC] font-montserrat text-sm font-medium cursor-pointer flex items-center justify-center gap-2 w-full mt-6 transition-colors duration-300 hover:text-white" 
              onClick={handleChangeEmailAddress}
            >
              <span>←</span> Change Email Address
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden font-montserrat bg-neutral-900">
      <div className="relative z-10 min-h-screen px-6 py-10 flex justify-center items-start">
        <button 
          className="absolute top-8 left-6 bg-transparent border-none text-[#D19049] font-montserrat text-base font-medium cursor-pointer flex items-center gap-2 transition-colors duration-300 hover:text-[#BF4129]" 
          onClick={onBack}
        >
          <span className="text-lg">←</span> Go Back
        </button>
        
        <div className="flex flex-col items-center p-6 gap-6 absolute w-[560px] min-h-[600px] bg-[#262626] rounded-3xl text-center top-20 max-w-[calc(100vw-48px)]">
          <div>
            <h1 className="text-white text-4xl font-bold m-0 leading-tight">Set a New Password</h1>
            <p className="text-[#CCCCCC] text-lg font-medium m-0 mt-4 leading-relaxed">Create a strong password for your account</p>
          </div>

          <div className="w-full text-left">
            <label htmlFor="new-password" className="block text-white text-sm font-medium mb-2">New Password</label>
            <div className="relative flex items-center">
              <input
                type={showPassword ? 'text' : 'password'}
                id="new-password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                placeholder="••••••••"
                className="w-full h-14 bg-[#404040] border border-[#525252] rounded-xl px-5 pr-12 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:bg-[#4A4A4A] placeholder-[#999999]"
              />
              <button
                type="button"
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-transparent border-none text-[#888888] cursor-pointer flex items-center justify-center z-10 hover:text-[#CCCCCC]"
                onClick={() => setShowPassword(!showPassword)}
              >
                👁
              </button>
            </div>
          </div>

          <div className="w-full text-left">
            <div className={`text-[#999999] text-sm my-2 flex items-center gap-2 ${passwordChecks.length ? 'text-[#4CAF50]' : ''}`}>
              <span className={`text-xs ${passwordChecks.length ? 'text-[#4CAF50]' : 'text-[#666666]'}`}>
                {passwordChecks.length ? '●' : '○'}
              </span>
              Be at least 8 characters long
            </div>
            <div className={`text-[#999999] text-sm my-2 flex items-center gap-2 ${passwordChecks.uppercase ? 'text-[#4CAF50]' : ''}`}>
              <span className={`text-xs ${passwordChecks.uppercase ? 'text-[#4CAF50]' : 'text-[#666666]'}`}>
                {passwordChecks.uppercase ? '●' : '○'}
              </span>
              Include at least one uppercase letter (A-Z)
            </div>
            <div className={`text-[#999999] text-sm my-2 flex items-center gap-2 ${passwordChecks.lowercase ? 'text-[#4CAF50]' : ''}`}>
              <span className={`text-xs ${passwordChecks.lowercase ? 'text-[#4CAF50]' : 'text-[#666666]'}`}>
                {passwordChecks.lowercase ? '●' : '○'}
              </span>
              Include at least one lowercase letter (a-z)
            </div>
            <div className={`text-[#999999] text-sm my-2 flex items-center gap-2 ${passwordChecks.number ? 'text-[#4CAF50]' : ''}`}>
              <span className={`text-xs ${passwordChecks.number ? 'text-[#4CAF50]' : 'text-[#666666]'}`}>
                {passwordChecks.number ? '●' : '○'}
              </span>
              Include at least one number (0-9)
            </div>
            <div className={`text-[#999999] text-sm my-2 flex items-center gap-2 ${passwordChecks.special ? 'text-[#4CAF50]' : ''}`}>
              <span className={`text-xs ${passwordChecks.special ? 'text-[#4CAF50]' : 'text-[#666666]'}`}>
                {passwordChecks.special ? '●' : '○'}
              </span>
              Include at least one special character (!@#$%^&*)
            </div>
          </div>

          <div className="w-full text-left">
            <label htmlFor="confirm-password" className="block text-white text-sm font-medium mb-2">Re-Enter New Password</label>
            <div className="relative flex items-center">
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirm-password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Re-enter your new password"
                className="w-full h-14 bg-[#404040] border border-[#525252] rounded-xl px-5 pr-12 text-white font-montserrat text-base transition-all duration-300 focus:outline-none focus:border-[#BF4129] focus:bg-[#4A4A4A] placeholder-[#999999]"
              />
              <button
                type="button"
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-transparent border-none text-[#888888] cursor-pointer flex items-center justify-center z-10 hover:text-[#CCCCCC]"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                👁
              </button>
            </div>
          </div>

          <button
            className="w-full h-14 bg-[#BF4129] border-none rounded-xl text-white font-montserrat text-base font-semibold cursor-pointer transition-all duration-300 hover:bg-[#D19049] hover:-translate-y-0.5 disabled:bg-[#666666] disabled:cursor-not-allowed disabled:transform-none"
            onClick={handleResetPassword}
            disabled={!allPasswordChecksValid || !passwordsMatch}
          >
            Reset Password
          </button>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
