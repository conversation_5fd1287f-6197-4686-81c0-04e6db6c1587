/* Trading Dashboard Main Container */
.trading-dashboard-main-container {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
}

.trading-dashboard-main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 2;
}

/* Go Back Button */
.go-back-btn {
  background: none;
  border: none;
  color: #BF4129;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 32px;
  transition: color 0.3s ease;
}

.go-back-btn:hover {
  color: #D14A2A;
}

.go-back-btn span {
  font-size: 18px;
}

/* Header Section */
.trading-dashboard-header {
  text-align: center;
  margin-bottom: 40px;
}

.trading-dashboard-title {
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 40px 0;
  text-align: center;
}



/* Success Alert */
.dashboard-success-alert {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 40px;
}

.success-icon {
  width: 24px;
  height: 24px;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-weight: 600;
  flex-shrink: 0;
}

.success-content {
  flex: 1;
}

.success-title {
  font-size: 16px;
  font-weight: 600;
  color: #4CAF50;
  margin-bottom: 4px;
}

.success-description {
  font-size: 14px;
  color: #CCCCCC;
  line-height: 1.5;
}

/* Dashboard Section Title */
.dashboard-section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 32px 0;
  text-align: center;
}

/* Trading Dashboard Cards */
.trading-dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.trading-dashboard-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.trading-dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.card-label {
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.card-change {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.card-change.positive {
  color: #4CAF50;
}

.card-change.negative {
  color: #FF6B6B;
}

.change-icon {
  font-size: 12px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .trading-dashboard-main-content {
    padding: 20px 16px;
  }

  .trading-dashboard-title {
    font-size: 32px;
    margin-bottom: 24px;
  }



  .dashboard-section-title {
    font-size: 24px;
    margin-bottom: 24px;
  }

  .trading-dashboard-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .card-value {
    font-size: 28px;
  }

  .dashboard-success-alert {
    padding: 16px;
  }
}
