'use client';

import { useRouter } from 'next/navigation';
import ForgotPassword from '../components/ForgotPassword';

export default function ForgotPasswordPage() {
  const router = useRouter();

  const handleBack = () => {
    router.push('/login');
  };

  return (
    <div className="w-full bg-neutral-900">
      <main className="w-full min-h-screen bg-neutral-900">
        <ForgotPassword onBack={handleBack} />
      </main>
    </div>
  );
}
