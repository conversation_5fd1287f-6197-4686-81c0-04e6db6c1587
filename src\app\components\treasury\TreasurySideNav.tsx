'use client';

import React from 'react';

interface SideNavItem {
  id: string;
  label: string;
  icon: string;
}

interface TreasurySideNavProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const TreasurySideNav: React.FC<TreasurySideNavProps> = ({ activeTab, onTabChange }) => {
  const sidebarItems: SideNavItem[] = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'live-reserve', label: 'Live Reserve', icon: '💰' },
    { id: 'daily-transactions', label: 'Daily Transactions', icon: '📈' },
    { id: 'real-time-staking', label: 'Real-Time Staking', icon: '🎯' },
    { id: 'startup-funding', label: 'Startup Funding', icon: '💼' },
    { id: 'roi-distribution', label: 'ROI Distribution', icon: '📊' },
    { id: 'visual-analytics', label: 'Visual Analytics', icon: '📈' }
  ];

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:block w-64 flex-shrink-0">
        <nav className="space-y-2">
          {sidebarItems.map((item) => (
            <button
              key={item.id}
              className={`w-full flex items-center gap-3 px-4 py-3 rounded-xl font-montserrat text-sm font-medium transition-all duration-300 ${
                activeTab === item.id
                  ? 'bg-[#BF4129] text-white'
                  : 'text-neutral-300 hover:text-white hover:bg-neutral-700 bg-[#262626]'
              }`}
              onClick={() => onTabChange(item.id)}
            >
              <span className="text-lg">{item.icon}</span>
              <span>{item.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Mobile Horizontal Tabs */}
      <div className="lg:hidden w-full mb-6">
        <div className="bg-[#262626] rounded-2xl p-2">
          <div className="flex flex-wrap gap-2">
            {sidebarItems.map((item) => (
              <button
                key={item.id}
                className={`px-3 py-2 rounded-xl font-montserrat text-xs font-medium transition-all duration-300 ${
                  activeTab === item.id
                    ? 'bg-[#BF4129] text-white'
                    : 'text-neutral-300 hover:text-white hover:bg-neutral-700'
                }`}
                onClick={() => onTabChange(item.id)}
              >
                <span className="mr-1">{item.icon}</span>
                <span className="hidden sm:inline">{item.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default TreasurySideNav;
