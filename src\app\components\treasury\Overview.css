/* Overview Content */
.overview-content {
  position: relative;
  z-index: 2;
}

/* Page Header */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 64px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.title-span {
  max-width: 300px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Content Wrapper */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

/* Section Header */
.overview-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin: 0 auto 50px auto !important;
  align-items: start;
  justify-content: center;
  max-width: fit-content;
}

/* Dashboard Cards */
.dashboard-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px 20px;
  width: 100%;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
}

.card-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 8px 0;
  line-height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.title-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-value {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8px;
}

.card-unit {
  font-size: 18px;
  font-weight: 500;
  color: #CCCCCC;
}

.card-change {
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.change-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.card-change.positive {
  color: #4CAF50;
}

.card-change.negative {
  color: #FF6B6B;
}

/* Desktop Responsive */
@media (min-width: 1200px) {
  .dashboard-cards {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1199px) {
  .dashboard-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 20px 20px 20px !important;
  }

  .page-title {
    font-size: 28px !important;
    gap: 8px;
    justify-content: center;
    margin-bottom: 12px !important;
  }

  .title-with-span {
    align-items: center;
  }

  .title-span {
    max-width: 120px;
  }

  .page-subtitle {
    font-size: 12px !important;
    line-height: 18px !important;
    margin-bottom: -10px !important;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px !important;
    gap: 16px !important;
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 12px !important;
  }

  .dashboard-content {
    order: 2;
    max-width: 100%;
    width: 100%;
    padding: 0px !important;
  }

  .dashboard-cards {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
    margin-top: -20px !important;
    margin-left: -12px !important;
    margin-right: -60px !important;
  }

  .dashboard-card {
    width: 105% !important;
    max-width: none !important;
    padding: 24px 16px 20px 16px !important;
    min-height: 120px !important;
    border-radius: 12px !important;
    text-align: left !important;
    margin-left: -5% !important;
    margin-right: -5% !important;
  }

  .card-title {
    font-size: 12px !important;
    font-weight: 500 !important;
    margin-top: 8px !important;
    margin-bottom: 12px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    justify-content: flex-start !important;
  }

  .card-value {
    font-size: 24px !important;
    font-weight: 700 !important;
    margin-bottom: 4px !important;
    line-height: 1.2 !important;
    justify-content: flex-start !important;
  }

  .card-unit {
    font-size: 14px !important;
    opacity: 0.8 !important;
    margin-left: 4px !important;
  }

  .card-change {
    font-size: 13px !important;
    margin-top: 4px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    gap: 4px !important;
  }

  .card-header {
    margin-bottom: 12px !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 4px !important;
  }

  .section-title {
    font-size: 18px !important;
    margin-bottom: 16px !important;
  }

  .overview-header {
    margin-bottom: 20px !important;
  }

  .overview-content {
    padding-bottom: 20px !important;
  }
}
