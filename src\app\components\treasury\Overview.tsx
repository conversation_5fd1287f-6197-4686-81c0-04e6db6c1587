'use client';

import React from 'react';
import './Overview.css';

const Overview: React.FC = () => {
  // Dashboard cards data matching the legacy design
  const dashboardCards = [
    {
      title: 'TOTAL AMOUNT OF ESVC STAKED',
      value: '177,874,389.00',
      unit: 'ESVC',
      change: '+2.4% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE FOR DAILY ISO PAYOUTS',
      value: '$609,185',
      change: '+7.8% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE TO FUND STARTUPS',
      value: '$43,700',
      change: '+4.8% Today',
      changeType: 'positive',
      hasInfoIcon: true
    },
    {
      title: 'TOTAL NUMBER OF STAKERS',
      value: '11,302',
      change: '+3.8% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL ESVC SOLD TO DATE',
      value: '$29,400,200.00',
      change: '+2.4% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL PROFIT GENERATED',
      value: '$43,700',
      change: '+1.8% Today',
      changeType: 'positive'
    }
  ];

  return (
    <div className="dashboard-content">
      {/* Section Header */}
      <div className="overview-header">
        <h2 className="section-title">Overview</h2>
      </div>

      {/* Dashboard Cards Grid */}
      <div className="dashboard-cards">
        {dashboardCards.map((card, index) => (
          <div key={index} className="dashboard-card">
            <div className="card-header">
              <h3 className="card-title">
                {card.title}
                {card.hasInfoIcon && (
                  <span className="title-icon">ℹ️</span>
                )}
              </h3>
            </div>
            <div className="card-content">
              <div className="card-value">
                {card.value}
                {card.unit && <span className="card-unit">{card.unit}</span>}
              </div>
              <div className={`card-change ${card.changeType}`}>
                <span className="change-icon">📈</span>
                {card.change}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Overview;
