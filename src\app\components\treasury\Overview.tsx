'use client';

import React from 'react';

const Overview: React.FC = () => {
  // Dashboard cards data matching the legacy design
  const dashboardCards = [
    {
      title: 'TOTAL AMOUNT OF ESVC STAKED',
      value: '177,874,389.00',
      unit: 'ESVC',
      change: '+2.4% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE FOR DAILY ISO PAYOUTS',
      value: '$609,185',
      change: '+7.8% Today',
      changeType: 'positive'
    },
    {
      title: 'FUNDS AVAILABLE TO FUND STARTUPS',
      value: '$43,700',
      change: '+4.8% Today',
      changeType: 'positive',
      hasInfoIcon: true
    },
    {
      title: 'TOTAL NUMBER OF STAKERS',
      value: '11,302',
      change: '+3.8% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL ESVC SOLD TO DATE',
      value: '$29,400,200.00',
      change: '+2.4% Today',
      changeType: 'positive'
    },
    {
      title: 'TOTAL PROFIT GENERATED',
      value: '$43,700',
      change: '+1.8% Today',
      changeType: 'positive'
    }
  ];

  return (
    <div>
      {/* Section Header */}
      <div className="mb-4">
        <h2 className="text-white text-3xl font-semibold font-montserrat">Overview</h2>
      </div>

      {/* Dashboard Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-fit mx-auto mb-12">
        {dashboardCards.map((card, index) => (
          <div key={index} className="bg-[#262626] rounded-2xl p-6 min-w-[280px] text-center">
            <div className="mb-4">
              <h3 className="text-neutral-400 text-xs font-montserrat uppercase tracking-wide flex items-center justify-center gap-2">
                {card.title}
                {card.hasInfoIcon && (
                  <span className="text-neutral-500 text-sm">ℹ️</span>
                )}
              </h3>
            </div>
            <div className="space-y-2">
              <div className="text-white text-2xl font-bold font-montserrat flex items-baseline justify-center gap-2">
                {card.value}
                {card.unit && <span className="text-lg font-medium text-neutral-300">{card.unit}</span>}
              </div>
              <div className={`text-sm font-medium flex items-center justify-center gap-1 ${
                card.changeType === 'positive' ? 'text-green-400' : 'text-red-400'
              }`}>
                <span>📈</span>
                {card.change}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Overview;
