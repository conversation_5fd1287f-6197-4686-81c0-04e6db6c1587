'use client';

import React from 'react';

const Overview: React.FC = () => {
  return (
    <div className="space-y-8">
      {/* Treasury Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Total Treasury Value</h3>
          <p className="text-white text-3xl font-bold font-montserrat">$2.4M</p>
          <p className="text-green-400 text-sm font-montserrat mt-2">+15.2% this month</p>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Active Stakers</h3>
          <p className="text-white text-3xl font-bold font-montserrat">1,247</p>
          <p className="text-blue-400 text-sm font-montserrat mt-2">+89 this week</p>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Total Staked ESVC</h3>
          <p className="text-white text-3xl font-bold font-montserrat">850K</p>
          <p className="text-green-400 text-sm font-montserrat mt-2">68% of supply</p>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-neutral-400 text-sm font-montserrat mb-2">Funded Startups</h3>
          <p className="text-white text-3xl font-bold font-montserrat">12</p>
          <p className="text-purple-400 text-sm font-montserrat mt-2">$480K deployed</p>
        </div>
      </div>

      {/* Treasury Composition */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Treasury Composition</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Asset Breakdown */}
          <div>
            <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Asset Allocation</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                  <span className="text-neutral-300 font-montserrat">Bitcoin (BTC)</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-montserrat font-semibold">$960K</p>
                  <p className="text-neutral-400 text-sm font-montserrat">40%</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                  <span className="text-neutral-300 font-montserrat">Ethereum (ETH)</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-montserrat font-semibold">$720K</p>
                  <p className="text-neutral-400 text-sm font-montserrat">30%</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                  <span className="text-neutral-300 font-montserrat">USDC</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-montserrat font-semibold">$480K</p>
                  <p className="text-neutral-400 text-sm font-montserrat">20%</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
                  <span className="text-neutral-300 font-montserrat">Other Assets</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-montserrat font-semibold">$240K</p>
                  <p className="text-neutral-400 text-sm font-montserrat">10%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Revenue Sources */}
          <div>
            <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Revenue Sources</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-[#BF4129] rounded-full"></div>
                  <span className="text-neutral-300 font-montserrat">Trading Bot Fees</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-montserrat font-semibold">$45K</p>
                  <p className="text-neutral-400 text-sm font-montserrat">Monthly</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                  <span className="text-neutral-300 font-montserrat">Staking Rewards</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-montserrat font-semibold">$28K</p>
                  <p className="text-neutral-400 text-sm font-montserrat">Monthly</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-cyan-500 rounded-full"></div>
                  <span className="text-neutral-300 font-montserrat">Investment Returns</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-montserrat font-semibold">$18K</p>
                  <p className="text-neutral-400 text-sm font-montserrat">Monthly</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 bg-pink-500 rounded-full"></div>
                  <span className="text-neutral-300 font-montserrat">Platform Fees</span>
                </div>
                <div className="text-right">
                  <p className="text-white font-montserrat font-semibold">$12K</p>
                  <p className="text-neutral-400 text-sm font-montserrat">Monthly</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-[#262626] rounded-2xl p-8">
        <h2 className="text-white text-2xl font-bold font-montserrat mb-6">Recent Treasury Activity</h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-neutral-800 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">+</span>
              </div>
              <div>
                <p className="text-white font-montserrat font-semibold">Trading Bot Revenue</p>
                <p className="text-neutral-400 text-sm font-montserrat">2 hours ago</p>
              </div>
            </div>
            <p className="text-green-400 font-montserrat font-bold">+$2,450</p>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-neutral-800 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">S</span>
              </div>
              <div>
                <p className="text-white font-montserrat font-semibold">Staking Rewards Distributed</p>
                <p className="text-neutral-400 text-sm font-montserrat">6 hours ago</p>
              </div>
            </div>
            <p className="text-blue-400 font-montserrat font-bold">$8,920</p>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-neutral-800 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">F</span>
              </div>
              <div>
                <p className="text-white font-montserrat font-semibold">Startup Funding Deployed</p>
                <p className="text-neutral-400 text-sm font-montserrat">1 day ago</p>
              </div>
            </div>
            <p className="text-purple-400 font-montserrat font-bold">-$50,000</p>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-neutral-800 rounded-xl">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">B</span>
              </div>
              <div>
                <p className="text-white font-montserrat font-semibold">Bitcoin Purchase</p>
                <p className="text-neutral-400 text-sm font-montserrat">2 days ago</p>
              </div>
            </div>
            <p className="text-orange-400 font-montserrat font-bold">+0.5 BTC</p>
          </div>
        </div>
      </div>

      {/* Treasury Health */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Liquidity Ratio</h3>
          <div className="flex items-center gap-4">
            <div className="flex-1 bg-neutral-700 rounded-full h-3">
              <div className="bg-green-500 h-3 rounded-full" style={{ width: '78%' }}></div>
            </div>
            <span className="text-green-400 font-montserrat font-bold">78%</span>
          </div>
          <p className="text-neutral-400 text-sm font-montserrat mt-2">Healthy liquidity levels</p>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Risk Score</h3>
          <div className="flex items-center gap-4">
            <div className="flex-1 bg-neutral-700 rounded-full h-3">
              <div className="bg-yellow-500 h-3 rounded-full" style={{ width: '35%' }}></div>
            </div>
            <span className="text-yellow-400 font-montserrat font-bold">Low</span>
          </div>
          <p className="text-neutral-400 text-sm font-montserrat mt-2">Conservative allocation</p>
        </div>
        
        <div className="bg-[#262626] rounded-2xl p-6">
          <h3 className="text-white text-lg font-semibold font-montserrat mb-4">Growth Rate</h3>
          <div className="flex items-center gap-4">
            <div className="flex-1 bg-neutral-700 rounded-full h-3">
              <div className="bg-blue-500 h-3 rounded-full" style={{ width: '65%' }}></div>
            </div>
            <span className="text-blue-400 font-montserrat font-bold">15.2%</span>
          </div>
          <p className="text-neutral-400 text-sm font-montserrat mt-2">Annual growth rate</p>
        </div>
      </div>
    </div>
  );
};

export default Overview;
